/**
 * Booking Page Module
 * 
 * Handles functionality for the booking.html page including:
 * - Iframe URL management
 * - Loading state management
 * - Navigation functions
 * - Error handling for cross-origin iframe issues
 */

/**
 * Navigate back to the previous page (booking form)
 */
function goBack() {
    // Go back to the previous page (booking form)
    window.history.back();
}

/**
 * Hide the loading overlay when iframe loads
 */
function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.style.opacity = '0';
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 500);
    }
}

/**
 * Set the iframe URL based on the URL parameter
 */
function setIframeUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const setmoreUrl = urlParams.get('url');
    const iframe = document.getElementById('booking-iframe');

    if (iframe) {
        // Set up iframe load event listener
        iframe.addEventListener('load', hideLoading);

        if (setmoreUrl) {
            // Decode the URL and set it as the iframe source
            iframe.src = decodeURIComponent(setmoreUrl);
            console.log('Loading Setmore URL:', decodeURIComponent(setmoreUrl));
        } else {
            // Fallback to default Setmore URL
            iframe.src = 'https://mobile-massage.setmore.com';
            console.log('Using default Setmore URL');
        }
    }
}

/**
 * Initialize the booking page functionality
 */
function initBookingPage() {
    // Set the iframe URL based on URL parameters
    setIframeUrl();

    // Set up back button event listener
    const backButton = document.getElementById('back-button');
    if (backButton) {
        backButton.addEventListener('click', goBack);
    }

    // Handle cases where iframe might not trigger onload
    setTimeout(() => {
        hideLoading();
    }, 5000); // Hide loading after 5 seconds regardless

    // Optional: Suppress cross-origin iframe errors during development
    // These errors come from Setmore's React app and don't affect your site
    window.addEventListener('error', function(e) {
        if (e.filename && e.filename.includes('framework-')) {
            e.preventDefault();
            return false;
        }
    });
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initBookingPage();
});

// Make functions available globally for onclick handlers
window.goBack = goBack;
window.hideLoading = hideLoading;
