const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Define the root directory for serving files
const ROOT_DIR = path.resolve(__dirname);
const PUBLIC_DIR = path.join(ROOT_DIR, 'public');

// WebSocket connections for auto-refresh
const wsConnections = [];

// Security: Track request counts for basic rate limiting
const requestCounts = {};
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const RATE_LIMIT_MAX = 1000; // Max requests per minute per IP - increased for local development

// Clean up rate limiting data periodically
setInterval(() => {
  const now = Date.now();
  for (const ip in requestCounts) {
    if (now > requestCounts[ip].resetTime) {
      delete requestCounts[ip];
    }
  }
}, 60000); // Clean up every minute

// Create HTTP server
const server = http.createServer((req, res) => {
  // Basic rate limiting
  const clientIP = req.socket.remoteAddress || 'unknown';
  if (!requestCounts[clientIP]) {
    requestCounts[clientIP] = { count: 1, resetTime: Date.now() + RATE_LIMIT_WINDOW };
  } else {
    // Reset count if window has passed
    if (Date.now() > requestCounts[clientIP].resetTime) {
      requestCounts[clientIP] = { count: 1, resetTime: Date.now() + RATE_LIMIT_WINDOW };
    } else {
      requestCounts[clientIP].count++;
      // Check if rate limit exceeded
      if (requestCounts[clientIP].count > RATE_LIMIT_MAX) {
        res.writeHead(429, { 'Content-Type': 'text/plain' });
        res.end('Too Many Requests');
        return;
      }
    }
  }

  // Parse the URL
  const parsedUrl = url.parse(req.url);

  // Security: Normalize the path to prevent directory traversal
  let sanitizedPath = path.normalize(parsedUrl.pathname).replace(/^(\.\.[/\\])+/, '');

  // Default to index file for root path
  if (sanitizedPath === '/' || sanitizedPath === '' || sanitizedPath === '\\') {
    sanitizedPath = 'index.html';
  }

  // Log the requested path for debugging
  console.log(`Requested path: ${sanitizedPath}`);

  // Construct the file path, ensuring it stays within allowed directories
  let filePath = path.join(ROOT_DIR, sanitizedPath);

  // Security: Ensure the file path is within the allowed directories
  // For development, we'll be more permissive but still log suspicious requests
  if (!filePath.startsWith(ROOT_DIR)) {
    console.warn(`Suspicious path request: ${filePath}`);
    // We'll continue processing instead of blocking for local development
  }

  // Check if the file exists in the public directory if not found in root
  if (!fs.existsSync(filePath)) {
    const publicFilePath = path.join(PUBLIC_DIR, sanitizedPath);
    if (fs.existsSync(publicFilePath)) {
      filePath = publicFilePath;
      console.log(`File found in public folder: ${filePath}`);
    } else {
      // Check if the file exists in the libs directory
      if (sanitizedPath.startsWith('/libs/')) {
        const libsPath = path.join(ROOT_DIR, sanitizedPath);
        if (fs.existsSync(libsPath)) {
          filePath = libsPath;
          console.log(`File found in libs folder: ${filePath}`);
        }
      } else if (sanitizedPath === 'index.html') {
        // For development, try looking directly for index.html
        const directPath = path.join(ROOT_DIR, 'index.html');
        if (fs.existsSync(directPath)) {
          filePath = directPath;
          console.log(`File found at direct path: ${filePath}`);
        }
      }
    }
  }

  // Check if the path is a directory
  fs.stat(filePath, (err, stats) => {
    if (err) {
      // File not found or other error
      console.log(`Not found: ${filePath}`);
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Not Found');
      return;
    }

    // Handle directory requests
    if (stats.isDirectory()) {
      console.log(`Directory requested: ${filePath}`);
      // For root directory, redirect to index.html
      if (filePath === ROOT_DIR) {
        const redirectPath = path.join(ROOT_DIR, 'index.html');
        if (fs.existsSync(redirectPath)) {
          console.log(`Redirecting to: ${redirectPath}`);
          fs.readFile(redirectPath, (error, content) => {
            if (error) {
              res.writeHead(500, { 'Content-Type': 'text/plain' });
              res.end('Internal Server Error');
            } else {
              res.writeHead(200, { 'Content-Type': 'text/html' });
              res.end(content, 'utf-8');
              console.log(`Serving: ${redirectPath} as text/html`);
            }
          });
          return;
        }
      }

      // For other directories, prevent listing
      console.warn(`Directory listing attempt: ${filePath}`);
      res.writeHead(403, { 'Content-Type': 'text/plain' });
      res.end('Forbidden');
      return;
    }

    // Get file extension and set content type
    const extname = String(path.extname(filePath)).toLowerCase();

    // Map file extensions to MIME types
    const mimeTypes = {
      '.html': 'text/html',
      '.js': 'text/javascript',
      '.css': 'text/css',
      '.json': 'application/json',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.webp': 'image/webp',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject',
      '.otf': 'font/otf',
      '.pdf': 'application/pdf',
      '.txt': 'text/plain',
      '.wasm': 'application/wasm',
    };

    const contentType = mimeTypes[extname] || 'application/octet-stream';

    // Read and serve the file
    fs.readFile(filePath, (error, content) => {
      if (error) {
        console.error(`Error serving ${filePath}: ${error.code}`);
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Internal Server Error');
      } else {
        // Set security headers
        const headers = {
          'Content-Type': contentType,
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'SAMEORIGIN',
          'X-XSS-Protection': '1; mode=block',
          'Content-Security-Policy': "default-src 'self'; connect-src 'self' ws: wss: https://unpkg.com; img-src 'self' data: https: blob:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com; worker-src 'self' blob:; frame-src https://mobile-massage.setmore.com https://*.setmore.com;",
          'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        };

        res.writeHead(200, headers);
        res.end(content, 'utf-8');
        console.log(`Serving: ${filePath} as ${contentType}`);
      }
    });
  });
});

const PORT = 3002; // Changed from 3001 to avoid conflicts

// Setup WebSocket server for auto-refresh functionality
server.on('upgrade', (request, socket, head) => {
  const pathname = url.parse(request.url).pathname;

  if (pathname === '/ws') {
    // WebSocket handshake
    const GUID = '258EAFA5-E914-47DA-95CA-C5AB0DC85B11';
    const key = request.headers['sec-websocket-key'];
    const acceptKey = require('crypto')
      .createHash('sha1')
      .update(key + GUID)
      .digest('base64');

    const headers = [
      'HTTP/1.1 101 Switching Protocols',
      'Upgrade: websocket',
      'Connection: Upgrade',
      `Sec-WebSocket-Accept: ${acceptKey}`,
      '\r\n'
    ].join('\r\n');

    socket.write(headers);

    // Add this connection to our list
    wsConnections.push(socket);

    // Handle socket closure
    socket.on('close', () => {
      const index = wsConnections.indexOf(socket);
      if (index !== -1) {
        wsConnections.splice(index, 1);
      }
    });

    // Simple error handling
    socket.on('error', (err) => {
      console.error('WebSocket error:', err);
    });
  } else {
    // Not a WebSocket request, close the connection
    socket.destroy();
  }
});

// Function to send refresh message to all connected WebSocket clients
function sendRefreshSignal() {
  const message = Buffer.from(JSON.stringify({ type: 'refresh' }));

  // WebSocket frame format: FIN=1, Opcode=1 (text), Mask=0
  const frameHeader = Buffer.from([0x81]);

  // Length byte
  let lengthBytes;
  if (message.length < 126) {
    lengthBytes = Buffer.from([message.length]);
  } else if (message.length < 65536) {
    lengthBytes = Buffer.from([126, (message.length >> 8) & 0xFF, message.length & 0xFF]);
  } else {
    lengthBytes = Buffer.from([127, 0, 0, 0, 0,
      (message.length >> 24) & 0xFF,
      (message.length >> 16) & 0xFF,
      (message.length >> 8) & 0xFF,
      message.length & 0xFF
    ]);
  }

  // Send the message to all connected clients
  wsConnections.forEach(socket => {
    try {
      socket.write(Buffer.concat([frameHeader, lengthBytes, message]));
    } catch (err) {
      console.error('Error sending refresh signal:', err);
    }
  });

  console.log(`Sent refresh signal to ${wsConnections.length} connected clients`);
}

// Watch for file changes
const filesToWatch = [
  path.join(ROOT_DIR, 'index.html'),
  path.join(ROOT_DIR, 'server.js'),
  path.join(ROOT_DIR, 'scripts/main.js'),
  path.join(ROOT_DIR, 'styles/global.css'),
  path.join(ROOT_DIR, 'styles/base.css'),
  path.join(ROOT_DIR, 'styles/components/card.css'),
  path.join(ROOT_DIR, 'styles/components/about.css'),
  path.join(ROOT_DIR, 'styles/components/services.css'),
  path.join(ROOT_DIR, 'styles/effects/holographic.css'),
  path.join(ROOT_DIR, 'styles/effects/shine.css'),
  path.join(ROOT_DIR, 'styles/effects/animations.css'),
  path.join(ROOT_DIR, 'styles/responsive.css'),
  path.join(ROOT_DIR, 'scripts/modules/card-effects.js'),
  path.join(ROOT_DIR, 'scripts/modules/utils.js'),
  path.join(ROOT_DIR, 'scripts/modules/device-detection.js'),
  path.join(ROOT_DIR, 'scripts/vendor/rive/rive.js')
];

filesToWatch.forEach(file => {
  fs.watch(file, (eventType) => {
    if (eventType === 'change') {
      console.log(`File changed: ${file}`);
      sendRefreshSignal();
    }
  });
  console.log(`Watching for changes: ${file}`);
});

// Security: Only listen on localhost, not all interfaces
server.listen(PORT, '127.0.0.1', () => {
  console.log(`Server running at http://localhost:${PORT}/`);
  console.log(`Serving files from: ${ROOT_DIR}`);
  console.log(`Public directory: ${PUBLIC_DIR}`);
  console.log('Auto-refresh enabled - WebSocket server running');
});

// Graceful shutdown
process.on('SIGINT', () => {
  // Close all WebSocket connections
  wsConnections.forEach(socket => {
    try {
      socket.end();
    } catch (err) {
      // Ignore errors during shutdown
    }
  });

  server.close(() => {
    console.log('Server shut down');
    process.exit(0);
  });
});


