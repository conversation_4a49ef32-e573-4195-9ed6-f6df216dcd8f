/**
 * Booking Form Dynamic Styles
 * 
 * This file contains CSS classes to replace all inline styles in booking-form.js
 * Created for Content Security Policy compliance - eliminates need for 'unsafe-inline'
 */

/*===========================================*/
/*           ANIMATION CONTROL CLASSES       */
/*===========================================*/

/* Animation Reset Class */
.reset-animation {
    animation: none !important;
}

/* Staggered Animation Class */
.staggered-animation {
    animation: slideInUp 0.6s ease forwards;
}

/* Animation Order CSS Custom Properties */
.animation-order-0 { --animation-order: 0; }
.animation-order-1 { --animation-order: 1; }
.animation-order-2 { --animation-order: 2; }
.animation-order-3 { --animation-order: 3; }
.animation-order-4 { --animation-order: 4; }
.animation-order-5 { --animation-order: 5; }
.animation-order-6 { --animation-order: 6; }
.animation-order-7 { --animation-order: 7; }
.animation-order-8 { --animation-order: 8; }
.animation-order-9 { --animation-order: 9; }
.animation-order-10 { --animation-order: 10; }

/*===========================================*/
/*           DISPLAY CONTROL CLASSES         */
/*===========================================*/

/* Force Block Display */
.force-block {
    display: block !important;
}

/* Force Hidden Display */
.force-hidden {
    display: none !important;
}

/*===========================================*/
/*           NOTIFICATION CLASSES            */
/*===========================================*/

/* Group Size Notification Base Styles */
.group-size-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, hsl(184, 70%, 35%), hsl(184, 70%, 45%));
    color: white;
    padding: 1rem 1.25rem;
    border-radius: 0.75rem;
    box-shadow: 0 10px 25px rgba(79, 209, 197, 0.4);
    z-index: 1000;
    max-width: 300px;
    font-family: 'Lato', sans-serif;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1.0);
}

/* Notification Visible State */
.notification-visible {
    opacity: 1 !important;
    transform: translateX(0) !important;
}

/* Notification Hidden State */
.notification-hidden {
    opacity: 0 !important;
    transform: translateX(100%) !important;
}

/*===========================================*/
/*           READY MESSAGE CLASSES           */
/*===========================================*/

/* Ready Message Enhanced Styling */
.ready-message-enhanced {
    padding: 1rem;
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 2px 8px rgba(79, 209, 197, 0.15);
    margin-bottom: 1.5rem;
}
