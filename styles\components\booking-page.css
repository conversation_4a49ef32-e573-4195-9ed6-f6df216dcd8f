/**
 * Booking Page Styles
 * 
 * This file contains styles that were previously in <style> tags in booking.html
 * Moved to external CSS for better Content Security Policy compliance
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Lato', sans-serif;
    background: linear-gradient(to top left, #4fd1c5, #e6fffa);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Booking Container */
.booking-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 600px;
    min-height: 1vh;
    position: relative;
}

/* Header Layout */
.booking-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: linear-gradient(135deg, hsl(184, 70%, 35%) 0%, hsl(184, 70%, 45%) 100%);
    color: rgb(255, 255, 255);
    padding: 2.5rem 1.5rem 1.5rem 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.booking-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    z-index: 1;
}

.booking-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.booking-header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.booking-header p {
    font-size: 1.1rem;
    opacity: 0.95;
    letter-spacing: 1px;
    position: relative;
    z-index: 1;
    margin-bottom: 0;
}

/* Back Button */
.booking-header-wrapper {
    position: relative;
    width: 100%;
}

.back-button {
    position: absolute;
    top: 1.2rem;
    left: 1.2rem;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Lato', sans-serif;
    font-weight: 500;
    z-index: 3;
    font-size: 1rem;
}

.back-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Iframe Container */
.iframe-container {
    position: relative;
    padding-bottom: 1200px;
    height: 0;
    overflow: hidden;
    background: #f8f9fa;
}

.iframe-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 1;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid hsl(184, 70%, 40%);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .booking-container {
        min-height: 90vh;
        border-radius: 15px;
    }

    .booking-header {
        padding: 2.5rem 1rem 1.2rem 1rem;
    }

    .booking-header h1 {
        font-size: 1.2rem;
    }

    .booking-header p {
        font-size: 0.95rem;
    }

    .back-button {
        top: 0.7rem;
        left: 0.7rem;
        padding: 0.5rem 0.8rem;
        font-size: 0.95rem;
    }

    .iframe-container {
        padding-bottom: 1400px;
    }
}
